<?php

/*
 Swift Mailer smoke test configuration.

 YOU ONLY NEED TO EDIT THIS FILE IF YOU WISH TO RUN THE SMOKE TESTS.

 The smoke tests are run by default when "All Tests" are run with the
 testing suite, however, without configuration options here only the unit tests
 will be run and the smoke tests will be skipped.
 */

/*
 Defines: The an address which <PERSON> can send to (it will also send "from" this address).
 Recommended: (your own email address?)
 */
define('SWIFT_SMOKE_EMAIL_ADDRESS', '<EMAIL>');

/*
 Defines: The specific transport you want to mail with.
 Recommended: Any of 'smtp', 'sendmail' or 'mail'
 */
define('SWIFT_SMOKE_TRANSPORT_TYPE', 'smtp');

// SMTP-specific settings

/*
 Defines: An SMTP server to connect to
 Recommended: smtp.your-isp.com (varies wildly!)
 */
define('SWIFT_SMOKE_SMTP_HOST', 'localhost');

/*
 Defines: The SMTP port to connect to
 Recommended: 25
 */
define('SWIFT_SMOKE_SMTP_PORT', '4456');

/*
 Defines: A username to authenticate with <PERSON><PERSON> (if needed).
 Recommended: (none)
 */
define('SWIFT_SMOKE_SMTP_USER', '');

/*
 Defines: A password to authenticate with SMTP (if needed).
 Recommended: (none)
 */
define('SWIFT_SMOKE_SMTP_PASS', '');

/*
 Defines: The encryption needed on your SMTP server.
 Recommended: (none), or 'tls' or 'ssl'
 */
define('SWIFT_SMOKE_SMTP_ENCRYPTION', '');

// Sendmail specific settings

/*
 Defines: The command to use when sending via sendmail
 Recommended: /usr/sbin/sendmail -bs (or "-oi -t")
 */
define('SWIFT_SMOKE_SENDMAIL_COMMAND', '/usr/sbin/sendmail -bs');
