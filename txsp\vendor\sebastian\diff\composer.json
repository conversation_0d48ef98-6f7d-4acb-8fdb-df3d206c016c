{"name": "sebastian/diff", "description": "Diff implementation", "keywords": ["diff"], "homepage": "https://github.com/sebastian<PERSON>mann/diff", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.0-dev"}}}