<?php

class Swift_Plugins_RedirectingPluginTest extends \PHPUnit\Framework\TestCase
{
    public function testRecipientCanBeSetAndFetched()
    {
        $plugin = new Swift_Plugins_RedirectingPlugin('<EMAIL>');
        $this->assertEquals('<EMAIL>', $plugin->getRecipient());
        $plugin->setRecipient('<EMAIL>');
        $this->assertEquals('<EMAIL>', $plugin->getRecipient());
    }

    public function testPluginChangesRecipients()
    {
        $message = (new Swift_Message())
            ->setSubject('...')
            ->setFrom(['<EMAIL>' => '<PERSON> Doe'])
            ->setTo($to = [
                '<EMAIL>' => 'Fabien (To)',
                '<EMAIL>' => 'Chris (To)',
            ])
            ->setCc($cc = [
                '<EMAIL>' => 'Fabien (Cc)',
                '<EMAIL>' => '<PERSON> (Cc)',
            ])
            ->setBcc($bcc = [
                '<EMAIL>' => 'Fabien (Bcc)',
                '<EMAIL>' => '<PERSON> (Bcc)',
            ])
            ->setBody('...')
        ;

        $plugin = new Swift_Plugins_RedirectingPlugin('<EMAIL>');

        $evt = $this->createSendEvent($message);

        $plugin->beforeSendPerformed($evt);

        $this->assertEquals($message->getTo(), ['<EMAIL>' => '']);
        $this->assertEquals($message->getCc(), []);
        $this->assertEquals($message->getBcc(), []);

        $plugin->sendPerformed($evt);

        $this->assertEquals($message->getTo(), $to);
        $this->assertEquals($message->getCc(), $cc);
        $this->assertEquals($message->getBcc(), $bcc);
    }

    public function testPluginRespectsUnsetToList()
    {
        $message = (new Swift_Message())
            ->setSubject('...')
            ->setFrom(['<EMAIL>' => 'John Doe'])
            ->setCc($cc = [
                '<EMAIL>' => 'Fabien (Cc)',
                '<EMAIL>' => 'Chris (Cc)',
            ])
            ->setBcc($bcc = [
                '<EMAIL>' => 'Fabien (Bcc)',
                '<EMAIL>' => 'Chris (Bcc)',
            ])
            ->setBody('...')
        ;

        $plugin = new Swift_Plugins_RedirectingPlugin('<EMAIL>');

        $evt = $this->createSendEvent($message);

        $plugin->beforeSendPerformed($evt);

        $this->assertEquals($message->getTo(), ['<EMAIL>' => '']);
        $this->assertEquals($message->getCc(), []);
        $this->assertEquals($message->getBcc(), []);

        $plugin->sendPerformed($evt);

        $this->assertEquals($message->getTo(), []);
        $this->assertEquals($message->getCc(), $cc);
        $this->assertEquals($message->getBcc(), $bcc);
    }

    public function testPluginRespectsAWhitelistOfPatterns()
    {
        $message = (new Swift_Message())
            ->setSubject('...')
            ->setFrom(['<EMAIL>' => 'John Doe'])
            ->setTo($to = [
                '<EMAIL>' => 'Fabien (To)',
                '<EMAIL>' => 'Chris (To)',
                '<EMAIL>' => 'Lars (To)',
            ])
            ->setCc($cc = [
                '<EMAIL>' => 'Fabien (Cc)',
                '<EMAIL>' => 'Chris (Cc)',
                '<EMAIL>' => 'Lars (Cc)',
            ])
            ->setBcc($bcc = [
                '<EMAIL>' => 'Fabien (Bcc)',
                '<EMAIL>' => 'Chris (Bcc)',
                '<EMAIL>' => 'John (Bcc)',
            ])
            ->setBody('...')
        ;

        $recipient = '<EMAIL>';
        $patterns = ['/^.*@internal.[a-z]+$/', '/^john-.*$/'];

        $plugin = new Swift_Plugins_RedirectingPlugin($recipient, $patterns);

        $this->assertEquals($recipient, $plugin->getRecipient());
        $this->assertEquals($plugin->getWhitelist(), $patterns);

        $evt = $this->createSendEvent($message);

        $plugin->beforeSendPerformed($evt);

        $this->assertEquals($message->getTo(), ['<EMAIL>' => 'Lars (To)', '<EMAIL>' => null]);
        $this->assertEquals($message->getCc(), ['<EMAIL>' => 'Lars (Cc)']);
        $this->assertEquals($message->getBcc(), ['<EMAIL>' => 'John (Bcc)']);

        $plugin->sendPerformed($evt);

        $this->assertEquals($message->getTo(), $to);
        $this->assertEquals($message->getCc(), $cc);
        $this->assertEquals($message->getBcc(), $bcc);
    }

    public function testArrayOfRecipientsCanBeExplicitlyDefined()
    {
        $message = (new Swift_Message())
            ->setSubject('...')
            ->setFrom(['<EMAIL>' => 'John Doe'])
            ->setTo([
            '<EMAIL>' => 'Fabien',
            '<EMAIL>' => 'Chris (To)',
            '<EMAIL>' => 'Lars (To)',
        ])
            ->setCc([
            '<EMAIL>' => 'Fabien',
            '<EMAIL>' => 'Chris (Cc)',
            '<EMAIL>' => 'Lars (Cc)',
        ])
            ->setBcc([
            '<EMAIL>' => 'Fabien',
            '<EMAIL>' => 'Chris (Bcc)',
            '<EMAIL>' => 'John (Bcc)',
        ])
            ->setBody('...')
        ;

        $recipients = ['<EMAIL>', '<EMAIL>'];
        $patterns = ['/^.*@internal.[a-z]+$/'];

        $plugin = new Swift_Plugins_RedirectingPlugin($recipients, $patterns);

        $evt = $this->createSendEvent($message);

        $plugin->beforeSendPerformed($evt);

        $this->assertEquals(
            $message->getTo(),
            ['<EMAIL>' => 'Fabien', '<EMAIL>' => 'Lars (To)', '<EMAIL>' => null]
        );
        $this->assertEquals(
            $message->getCc(),
            ['<EMAIL>' => 'Fabien', '<EMAIL>' => 'Lars (Cc)']
        );
        $this->assertEquals($message->getBcc(), ['<EMAIL>' => 'Fabien']);
    }

    private function createSendEvent(Swift_Mime_SimpleMessage $message)
    {
        $evt = $this->getMockBuilder('Swift_Events_SendEvent')
                    ->disableOriginalConstructor()
                    ->getMock();
        $evt->expects($this->any())
            ->method('getMessage')
            ->will($this->returnValue($message));

        return $evt;
    }
}
