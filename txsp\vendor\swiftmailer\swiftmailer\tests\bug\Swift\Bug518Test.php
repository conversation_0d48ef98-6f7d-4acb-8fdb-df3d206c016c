<?php

use Mo<PERSON>y as m;

class Swift_Bug518Test extends \SwiftMailerTestCase
{
    public function testIfEmailChangesAfterQueued()
    {
        $failedRecipients = 'value';
        $message = new Swift_Message();
        $message->setTo('<EMAIL>');

        $that = $this;
        $messageValidation = function ($m) use ($that) {
            //the getTo should return the same value as we put in
            $that->assertEquals('<EMAIL>', key($m->getTo()), 'The message has changed after it was put to the memory queue');

            return true;
        };

        $transport = m::mock('Swift_Transport');
        $transport->shouldReceive('isStarted')->andReturn(true);
        $transport->shouldReceive('send')
            ->with(m::on($messageValidation), $failedRecipients)
            ->andReturn(1);

        $memorySpool = new Swift_MemorySpool();
        $memorySpool->queueMessage($message);

        /*
         * The message is queued in memory.
         * Lets change the message
         */
        $message->setTo('<EMAIL>');

        $memorySpool->flushQueue($transport, $failedRecipients);
    }
}
