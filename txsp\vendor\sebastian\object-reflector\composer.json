{"name": "sebastian/object-reflector", "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}