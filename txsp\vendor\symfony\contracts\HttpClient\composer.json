{"name": "symfony/http-client-contracts", "type": "library", "description": "Generic abstractions related to HTTP clients", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": "^7.1.3"}, "suggest": {"symfony/http-client-implementation": ""}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}