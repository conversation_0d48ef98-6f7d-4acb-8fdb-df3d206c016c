{"name": "symfony/browser-kit", "type": "library", "description": "Symfony BrowserKit Component", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": "^7.1.3", "symfony/dom-crawler": "~3.4|~4.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0", "symfony/http-client": "^4.3", "symfony/mime": "^4.3", "symfony/process": "~3.4|~4.0"}, "suggest": {"symfony/process": ""}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}}