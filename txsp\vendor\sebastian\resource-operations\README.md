# Resource Operations

Provides a list of PHP built-in functions that operate on resources.

## Installation

To add this component as a local, per-project dependency to your project, simply add a dependency on `sebastian/resource-operations` to your project's `composer.json` file. Here is a minimal example of a `composer.json` file that just defines a dependency on this component:

```JSON
{
    "require": {
        "sebastian/resource-operations": "~1.0"
    }
}
```

