<?php

/*
 Swift Mailer acceptance test configuration.

 YOU ONLY NEED TO EDIT THIS FILE IF YOU WISH TO RUN THE ACCEPTANCE TESTS.

 The acceptance tests are run by default when "All Tests" are run with the
 testing suite, however, without configuration options here only the unit tests
 will be run and the acceptance tests will be skipped.
 
 You can fill out only the parts you know and leave the other bits.
 */

/*
 Defines: The name and port of a SMTP server you can connect to.
 Recommended: smtp.gmail.com:25
 */
define('SWIFT_SMTP_HOST', 'localhost:4456');

/*
 Defines: An SMTP server and port which uses TLS encryption.
 Recommended: smtp.gmail.com:465
 */
define('SWIFT_TLS_HOST', 'smtp.gmail.com:465');

/*
 Defines: An SMTP server and port which uses SSL encryption.
 Recommended: smtp.gmail.com:465
 */
define('SWIFT_SSL_HOST', 'smtp.gmail.com:465');

/*
 Defines: The path to a sendmail binary (one which can run in -bs mode).
 Recommended: /usr/sbin/sendmail
 */
define('SWIFT_SENDMAIL_PATH', '/usr/sbin/sendmail -bs');
