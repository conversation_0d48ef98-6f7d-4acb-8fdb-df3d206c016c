<PERSON> Mailer
------------

Swift Mailer is a component based mailing solution for PHP 7.
It is released under the MIT license.

Homepage:      https://swiftmailer.symfony.com/
Documentation: https://swiftmailer.symfony.com/docs/introduction.html
Bugs:          https://github.com/swiftmailer/swiftmailer/issues
Repository:    https://github.com/swiftmailer/swiftmailer

Swift Mailer is highly object-oriented by design and lends itself
to use in complex web application with a great deal of flexibility.

For full details on usage, see the documentation.
