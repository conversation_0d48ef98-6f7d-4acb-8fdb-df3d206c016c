{"name": "sebastian/environment", "description": "Provides functionality to handle HHVM/PHP environments", "keywords": ["environment", "hhvm", "xdebug"], "homepage": "http://www.github.com/sebastianbergmann/environment", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.1"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}}