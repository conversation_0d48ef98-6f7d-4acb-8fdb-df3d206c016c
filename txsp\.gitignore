# phpstorm project files
.idea

# netbeans project files
nbproject

# zend studio for eclipse project files
.buildpath
.project
.settings

# windows thumbnail cache
Thumbs.db

# composer vendor dir
# /vendor

# composer files
# /composer.lock

# composer itself is not needed
composer.phar

# Mac DS_Store Files
.DS_Store

# phpunit itself is not needed
phpunit.phar
# local phpunit config
/phpunit.xml

tests/_output/*
tests/_support/_generated

#vagrant folder
/.vagrant

#local config
/config/local.php
/config/db.php
.env
/controllers/UpdateController.php
/jobs/UpdateJob.php

#install lock file
/install.lock

/web/temp
/web/csv
favicon.ico

#vv test 0218 2
.tags
.tags_sorted_by_file

/commands/UpdateController.php
#h5和公众号动态生成的文件
/plugins/mobile/h5
/plugins/wechat/h5
/plugins/mobile/h5/
/plugins/wechat/h5/
/plugins/mobile/we_h5/
/plugins/wechat/we_h5/
